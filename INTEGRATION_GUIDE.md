# 钓点可见性功能集成指南

## 🎯 功能概述

本功能为钓鱼应用添加了完整的钓点可见性控制系统，支持4种可见性级别：

1. **私有** - 只有创建者可见
2. **好友可见** - 创建者的好友可见  
3. **条件可见** - 符合特定条件的用户可见（积分赠送、等级要求）
4. **完全公开** - 所有用户可见

## 🔧 集成步骤

### 1. 数据库迁移

首先需要在PocketBase中执行数据库迁移：

```sql
-- 扩展钓点表
ALTER TABLE fishing_spots ADD COLUMN visibility TEXT DEFAULT 'PUBLIC';
ALTER TABLE fishing_spots ADD COLUMN visibility_conditions TEXT;
ALTER TABLE fishing_spots ADD COLUMN visibility_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- 创建积分赠送记录表
CREATE TABLE user_point_donations (
    id TEXT PRIMARY KEY,
    donor_id TEXT NOT NULL,
    recipient_id TEXT NOT NULL,
    points INTEGER NOT NULL,
    reason TEXT,
    spot_id TEXT,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (donor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (spot_id) REFERENCES fishing_spots(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_fishing_spots_visibility ON fishing_spots(visibility);
CREATE INDEX idx_point_donations_donor ON user_point_donations(donor_id);
```

### 2. 更新FishingSpot模型

在 `lib/models/fishing_spot.dart` 中添加可见性字段：

```dart
import '../models/spot_visibility.dart';

class FishingSpot {
  // ... 现有字段 ...
  
  /// 可见性级别
  final SpotVisibility visibility;
  
  /// 可见性条件（JSON格式）
  final Map<String, dynamic>? visibilityConditions;
  
  /// 可见性更新时间
  final DateTime visibilityUpdatedAt;

  FishingSpot({
    // ... 现有参数 ...
    this.visibility = SpotVisibility.public,
    this.visibilityConditions,
    DateTime? visibilityUpdatedAt,
  }) : visibilityUpdatedAt = visibilityUpdatedAt ?? DateTime.now();

  factory FishingSpot.fromJson(Map<String, dynamic> json) {
    return FishingSpot(
      // ... 现有字段映射 ...
      visibility: SpotVisibility.fromString(json['visibility'] ?? 'PUBLIC'),
      visibilityConditions: json['visibility_conditions'] != null
          ? (json['visibility_conditions'] is String
              ? jsonDecode(json['visibility_conditions'])
              : json['visibility_conditions'])
          : null,
      visibilityUpdatedAt: json['visibility_updated_at'] != null
          ? DateTime.parse(json['visibility_updated_at'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // ... 现有字段映射 ...
      'visibility': visibility.value,
      'visibility_conditions': visibilityConditions != null
          ? jsonEncode(visibilityConditions)
          : null,
      'visibility_updated_at': visibilityUpdatedAt.toIso8601String(),
    };
  }
}
```

### 3. 注册可见性服务

在 `lib/services/services.dart` 中注册新服务：

```dart
import 'spot_visibility_service.dart';

class Services {
  // ... 现有服务 ...
  
  /// 钓点可见性服务
  static SpotVisibilityService get spotVisibility => SpotVisibilityService();
  
  /// 初始化所有服务
  static Future<void> initialize() async {
    // ... 现有初始化代码 ...
    
    debugPrint('初始化钓点可见性服务');
    // SpotVisibilityService 是单例，不需要特殊初始化
    debugPrint('钓点可见性服务初始化完成');
  }
}
```

### 4. 更新钓点服务

在 `lib/services/fishing_spot_service_new.dart` 中添加可见性过滤：

```dart
/// 获取用户可见的钓点列表
Future<List<FishingSpot>> getVisibleSpots({
  LatLng? center,
  double? radiusKm,
  int page = 1,
  int perPage = 30,
}) async {
  try {
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      // 未登录用户只能看到公开钓点
      return await _getPublicSpots(center: center, radiusKm: radiusKm, page: page, perPage: perPage);
    }

    // 构建基础过滤条件
    String filter = _buildVisibilityFilter(currentUser.id);
    
    // 添加地理位置过滤
    if (center != null && radiusKm != null) {
      final bounds = _calculateBounds(center, radiusKm);
      filter += ' && latitude >= ${bounds['minLat']} && latitude <= ${bounds['maxLat']} && longitude >= ${bounds['minLng']} && longitude <= ${bounds['maxLng']}';
    }

    final records = await pb.collection('fishing_spots').getList(
      page: page,
      perPage: perPage,
      filter: filter,
      sort: '-created',
      expand: 'user_id',
    );

    final spots = _convertRecordsToSpots(records.items);
    
    // 进一步过滤条件可见的钓点
    final visibleSpots = <FishingSpot>[];
    for (final spot in spots) {
      if (await Services.spotVisibility.canUserViewSpot(currentUser.id, spot)) {
        visibleSpots.add(spot);
      }
    }

    return visibleSpots;
  } catch (e) {
    debugPrint('获取可见钓点失败: $e');
    return [];
  }
}

String _buildVisibilityFilter(String userId) {
  return '''(
    visibility = 'PUBLIC' ||
    (visibility = 'PRIVATE' && user_id = '$userId') ||
    (visibility = 'FRIENDS_ONLY' && (
      user_id = '$userId' ||
      user_id IN (SELECT following_id FROM user_follows WHERE follower_id = '$userId')
    )) ||
    visibility = 'CONDITIONAL'
  )''';
}
```

### 5. 更新钓点创建/编辑页面

在钓点创建或编辑页面中集成可见性选择器：

```dart
import '../widgets/spot_visibility_selector.dart';
import '../models/spot_visibility.dart';

class CreateSpotPage extends StatefulWidget {
  // ... 现有代码 ...
}

class _CreateSpotPageState extends State<CreateSpotPage> {
  // ... 现有字段 ...
  
  SpotVisibility _visibility = SpotVisibility.public;
  Map<String, dynamic>? _visibilityConditions;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // ... 现有代码 ...
      body: Form(
        child: Column(
          children: [
            // ... 现有表单字段 ...
            
            // 可见性设置
            SpotVisibilitySelector(
              initialVisibility: _visibility,
              initialConditions: _visibilityConditions,
              onChanged: (visibility, conditions) {
                setState(() {
                  _visibility = visibility;
                  _visibilityConditions = conditions;
                });
              },
            ),
            
            // ... 其他字段 ...
          ],
        ),
      ),
    );
  }

  Future<void> _createSpot() async {
    // ... 现有创建逻辑 ...
    
    final spotData = {
      // ... 现有字段 ...
      'visibility': _visibility.value,
      'visibility_conditions': _visibilityConditions != null 
          ? jsonEncode(_visibilityConditions) 
          : null,
    };
    
    // 创建钓点
    await pb.collection('fishing_spots').create(body: spotData);
  }
}
```

### 6. 更新钓点列表显示

在钓点列表中显示可见性指示器：

```dart
import '../widgets/spot_visibility_selector.dart';

Widget buildSpotCard(FishingSpot spot) {
  return Card(
    child: ListTile(
      title: Row(
        children: [
          Expanded(child: Text(spot.name)),
          SpotVisibilityIndicator(
            visibility: spot.visibility,
            showLabel: false,
          ),
        ],
      ),
      subtitle: Text(spot.description),
      // ... 其他内容 ...
    ),
  );
}
```

### 7. 添加积分赠送功能

创建积分赠送页面或对话框：

```dart
class PointDonationDialog extends StatefulWidget {
  final String recipientId;
  final String? spotId;

  const PointDonationDialog({
    Key? key,
    required this.recipientId,
    this.spotId,
  }) : super(key: key);

  @override
  State<PointDonationDialog> createState() => _PointDonationDialogState();
}

class _PointDonationDialogState extends State<PointDonationDialog> {
  final _pointsController = TextEditingController();
  final _reasonController = TextEditingController();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('赠送积分'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _pointsController,
            decoration: const InputDecoration(
              labelText: '积分数量',
              suffixText: '积分',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _reasonController,
            decoration: const InputDecoration(
              labelText: '赠送原因（可选）',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _donatePoints,
          child: _isLoading 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('赠送'),
        ),
      ],
    );
  }

  Future<void> _donatePoints() async {
    final points = int.tryParse(_pointsController.text);
    if (points == null || points <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的积分数量')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await Services.spotVisibility.donatePoints(
        widget.recipientId,
        points,
        reason: _reasonController.text.trim().isEmpty 
            ? null 
            : _reasonController.text.trim(),
        spotId: widget.spotId,
      );

      if (success) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('积分赠送成功')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('积分赠送失败')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('积分赠送失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
```

## 🧪 测试建议

### 1. 功能测试
- 测试不同可见性级别的钓点是否正确显示/隐藏
- 测试可见性条件是否正确生效
- 测试积分赠送功能
- 测试好友关系对可见性的影响

### 2. 性能测试
- 测试大量钓点的可见性检查性能
- 测试缓存机制是否有效
- 测试数据库查询性能

### 3. 边界情况测试
- 测试空条件的处理
- 测试无效数据的处理
- 测试网络异常情况

## 📊 监控指标

建议监控以下指标：
- 可见性检查的响应时间
- 缓存命中率
- 积分赠送成功率
- 不同可见性级别的钓点分布

## 🔒 安全注意事项

1. **权限验证**：确保只有钓点创建者可以修改可见性设置
2. **数据验证**：验证可见性条件的合法性
3. **防刷机制**：防止恶意的积分赠送或权限检查请求
4. **缓存安全**：确保缓存不会泄露敏感信息

通过以上步骤，您就可以成功集成钓点可见性功能到现有的钓鱼应用中。
