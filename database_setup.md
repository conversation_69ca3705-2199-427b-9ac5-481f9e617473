# 数据库设置指南

## 需要创建的表结构

### 1. 修改 fishing_spots 表

在 PocketBase 管理界面中，为 `fishing_spots` 表添加以下字段：

#### likes 字段
- **Name**: `likes`
- **Type**: `Number`
- **Required**: ✅
- **Min**: 0
- **Max**: (留空)
- **No decimal**: ✅
- **Default value**: 0

#### unlikes 字段
- **Name**: `unlikes`
- **Type**: `Number`
- **Required**: ✅
- **Min**: 0
- **Max**: (留空)
- **No decimal**: ✅
- **Default value**: 0

### 2. 创建 user_likes 表

#### 基本设置
- **Collection name**: `user_likes`
- **Collection type**: `Base`

#### Schema 字段

**user_id 字段**:
- **Name**: `user_id`
- **Type**: `Relation`
- **Required**: ✅
- **Collection**: `users`
- **Max select**: 1
- **Cascade delete**: ✅

**spot_id 字段**:
- **Name**: `spot_id`
- **Type**: `Relation`
- **Required**: ✅
- **Collection**: `fishing_spots`
- **Max select**: 1
- **Cascade delete**: ✅

#### API Rules
- **List rule**: `@request.auth.id != ""`
- **View rule**: `@request.auth.id != ""`
- **Create rule**: `@request.auth.id != "" && @request.auth.id = user_id`
- **Update rule**: `@request.auth.id != "" && @request.auth.id = user_id`
- **Delete rule**: `@request.auth.id != "" && @request.auth.id = user_id`

#### 索引
在 "Indexes" 部分添加：
```sql
CREATE UNIQUE INDEX idx_user_likes_unique ON user_likes (user_id, spot_id);
```

### 3. 创建 user_unlikes 表

#### 基本设置
- **Collection name**: `user_unlikes`
- **Collection type**: `Base`

#### Schema 字段
（与 user_likes 表完全相同）

**user_id 字段**:
- **Name**: `user_id`
- **Type**: `Relation`
- **Required**: ✅
- **Collection**: `users`
- **Max select**: 1
- **Cascade delete**: ✅

**spot_id 字段**:
- **Name**: `spot_id`
- **Type**: `Relation`
- **Required**: ✅
- **Collection**: `fishing_spots`
- **Max select**: 1
- **Cascade delete**: ✅

#### API Rules
- **List rule**: `@request.auth.id != ""`
- **View rule**: `@request.auth.id != ""`
- **Create rule**: `@request.auth.id != "" && @request.auth.id = user_id`
- **Update rule**: `@request.auth.id != "" && @request.auth.id = user_id`
- **Delete rule**: `@request.auth.id != "" && @request.auth.id = user_id`

#### 索引
在 "Indexes" 部分添加：
```sql
CREATE UNIQUE INDEX idx_user_unlikes_unique ON user_unlikes (user_id, spot_id);
```

## 创建步骤

### 步骤 1: 登录 PocketBase 管理界面
访问：`http://117.72.60.131:8090/_/`

### 步骤 2: 修改 fishing_spots 表
1. 进入 Collections → fishing_spots
2. 点击 "Edit collection"
3. 在 Schema 部分点击 "Add field"
4. 按照上面的规格添加 `likes` 和 `unlikes` 字段
5. 点击 "Save changes"

### 步骤 3: 创建 user_likes 表
1. 点击 "New collection"
2. 按照上面的规格创建表和字段
3. 设置 API Rules
4. 添加唯一索引
5. 点击 "Save"

### 步骤 4: 创建 user_unlikes 表
1. 重复步骤 3，但使用 `user_unlikes` 作为表名

### 步骤 5: 验证设置
创建完成后，您应该看到：
- `fishing_spots` 表有 `likes` 和 `unlikes` 字段
- `user_likes` 表有 `user_id` 和 `spot_id` 字段
- `user_unlikes` 表有 `user_id` 和 `spot_id` 字段
- 所有表都有正确的权限规则和索引

## 测试数据库设置

创建完表后，您可以运行应用程序测试点赞功能：

1. 启动应用：`flutter run`
2. 登录用户账户
3. 打开钓点详情页面
4. 测试点赞和倒赞功能
5. 检查控制台输出是否有错误

## 常见问题

### 问题 1: "Missing collection context" 错误
**解决方案**: 确保所有表都已正确创建，表名拼写正确。

### 问题 2: 权限错误
**解决方案**: 检查 API Rules 是否正确设置，确保用户已登录。

### 问题 3: 唯一约束错误
**解决方案**: 检查唯一索引是否正确创建。

## 数据库结构图

```
fishing_spots
├── id (主键)
├── name
├── latitude
├── longitude
├── likes (新增)
└── unlikes (新增)

user_likes
├── id (主键)
├── user_id (外键 → users.id)
└── spot_id (外键 → fishing_spots.id)

user_unlikes
├── id (主键)
├── user_id (外键 → users.id)
└── spot_id (外键 → fishing_spots.id)
```

完成这些设置后，点赞功能就可以正常工作了！
