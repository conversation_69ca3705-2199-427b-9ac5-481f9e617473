import 'package:flutter/material.dart';
import '../models/fishing_spot.dart';
import '../models/spot_visibility.dart';
import '../widgets/spot_visibility_selector.dart';
import '../services/service_locator.dart';

/// 钓点可见性功能演示页面
///
/// 用于测试和演示钓点可见性功能
class SpotVisibilityDemoPage extends StatefulWidget {
  const SpotVisibilityDemoPage({Key? key}) : super(key: key);

  @override
  State<SpotVisibilityDemoPage> createState() => _SpotVisibilityDemoPageState();
}

class _SpotVisibilityDemoPageState extends State<SpotVisibilityDemoPage> {
  SpotVisibility _selectedVisibility = SpotVisibility.public;
  Map<String, dynamic>? _visibilityConditions;
  List<FishingSpot> _spots = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSpots();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('钓点可见性演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能说明
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700]),
                        const SizedBox(width: 8),
                        const Text(
                          '钓点可见性功能',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '此功能允许用户设置钓点的可见性级别：\n'
                      '• 私有：只有创建者可见\n'
                      '• 好友可见：创建者的好友可见\n'
                      '• 条件可见：符合特定条件的用户可见\n'
                      '• 完全公开：所有用户可见',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 可见性设置演示
            const Text(
              '可见性设置演示',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            SpotVisibilitySelector(
              initialVisibility: _selectedVisibility,
              initialConditions: _visibilityConditions,
              onChanged: (visibility, conditions) {
                setState(() {
                  _selectedVisibility = visibility;
                  _visibilityConditions = conditions;
                });
              },
            ),

            const SizedBox(height: 20),

            // 当前设置显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前设置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        SpotVisibilityIndicator(
                          visibility: _selectedVisibility,
                          showLabel: true,
                        ),
                      ],
                    ),
                    if (_visibilityConditions != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        '条件: ${_getConditionDescription()}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 钓点列表
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '钓点列表',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _loadSpots,
                  icon: const Icon(Icons.refresh),
                  label: const Text('刷新'),
                ),
              ],
            ),
            const SizedBox(height: 12),

            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_spots.isEmpty)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Center(child: Text('暂无钓点数据')),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _spots.length,
                itemBuilder: (context, index) {
                  final spot = _spots[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.blue[100],
                        child: Icon(Icons.location_on, color: Colors.blue[700]),
                      ),
                      title: Row(
                        children: [
                          Expanded(child: Text(spot.name)),
                          SpotVisibilityIndicator(
                            visibility: spot.visibility,
                            showLabel: false,
                          ),
                        ],
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (spot.description.isNotEmpty)
                            Text(
                              spot.description,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          const SizedBox(height: 4),
                          Text(
                            '可见性: ${spot.visibility.displayName}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.settings),
                        onPressed: () => _showSpotSettings(spot),
                      ),
                    ),
                  );
                },
              ),

            const SizedBox(height: 20),

            // 测试按钮
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '测试功能',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _testVisibilityCheck,
                          child: const Text('测试可见性检查'),
                        ),
                        ElevatedButton(
                          onPressed: _testPointDonation,
                          child: const Text('测试积分赠送'),
                        ),
                        ElevatedButton(
                          onPressed: _testPayToView,
                          child: const Text('测试付费查看'),
                        ),
                        ElevatedButton(
                          onPressed: _clearCache,
                          child: const Text('清除缓存'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 加载钓点数据
  Future<void> _loadSpots() async {
    setState(() => _isLoading = true);

    try {
      // 使用新的可见性过滤方法
      final spots = await Services.fishingSpot.getVisibleSpots(perPage: 20);

      setState(() {
        _spots = spots;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载钓点失败: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 获取条件描述
  String _getConditionDescription() {
    if (_visibilityConditions == null) return '';

    final type = _visibilityConditions!['type'];
    switch (type) {
      case 'POINTS_DONATED':
        final minPoints = _visibilityConditions!['minPoints'] ?? 0;
        return '需要赠送 $minPoints 积分';
      case 'LEVEL_REQUIRED':
        final minLevel = _visibilityConditions!['minLevel'] ?? 1;
        return '需要达到 $minLevel 级';
      case 'PAY_TO_VIEW':
        final price = _visibilityConditions!['price'] ?? 0;
        return '需要支付 $price 积分';
      default:
        return '未知条件';
    }
  }

  /// 显示钓点设置对话框
  void _showSpotSettings(FishingSpot spot) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('钓点设置: ${spot.name}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('当前可见性: ${spot.visibility.displayName}'),
                const SizedBox(height: 8),
                if (spot.visibilityConditions != null)
                  Text('条件: ${_getSpotConditionDescription(spot)}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  /// 获取钓点条件描述
  String _getSpotConditionDescription(FishingSpot spot) {
    final conditions = spot.visibilityConditions;
    if (conditions == null) return '';

    final type = conditions['type'];
    switch (type) {
      case 'POINTS_DONATED':
        final minPoints = conditions['minPoints'] ?? 0;
        return '需要赠送 $minPoints 积分';
      case 'LEVEL_REQUIRED':
        final minLevel = conditions['minLevel'] ?? 1;
        return '需要达到 $minLevel 级';
      case 'PAY_TO_VIEW':
        final price = conditions['price'] ?? 0;
        return '需要支付 $price 积分';
      default:
        return '未知条件';
    }
  }

  /// 测试可见性检查
  Future<void> _testVisibilityCheck() async {
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先登录')));
      return;
    }

    if (_spots.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('没有钓点数据')));
      return;
    }

    final spot = _spots.first;
    final hasAccess = await Services.spotVisibility.canUserViewSpot(
      currentUser.id,
      spot,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('钓点 "${spot.name}" 访问权限: ${hasAccess ? "有权限" : "无权限"}'),
        ),
      );
    }
  }

  /// 测试积分赠送
  Future<void> _testPointDonation() async {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('积分赠送功能需要在实际应用中实现')));
  }

  /// 清除缓存
  void _clearCache() {
    Services.spotVisibility.clearAccessCache();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('可见性缓存已清除')));
  }
}
