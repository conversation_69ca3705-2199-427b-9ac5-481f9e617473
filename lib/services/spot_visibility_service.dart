import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/fishing_spot.dart';
import '../models/spot_visibility.dart';
import 'service_locator.dart';
import '../config/pocketbase_config.dart';

/// 钓点可见性管理服务
///
/// 负责：
/// - 检查用户对钓点的访问权限
/// - 管理可见性条件
/// - 缓存访问权限结果
/// - 处理可见性设置
class SpotVisibilityService {
  // 单例模式
  static final SpotVisibilityService _instance =
      SpotVisibilityService._internal();
  factory SpotVisibilityService() => _instance;
  SpotVisibilityService._internal();

  // 内存缓存 - 存储最近的访问权限检查结果
  final Map<String, Map<String, bool>> _memoryCache = {};

  // 缓存过期时间（分钟）
  static const int _cacheExpirationMinutes = 10;

  // 本地存储键前缀
  static const String _cacheKeyPrefix = 'spot_access_';

  /// 检查用户是否可以查看指定钓点
  ///
  /// [userId] 用户ID
  /// [spot] 钓点对象
  /// [useCache] 是否使用缓存，默认true
  ///
  /// 返回是否有访问权限
  Future<bool> canUserViewSpot(
    String userId,
    FishingSpot spot, {
    bool useCache = true,
  }) async {
    final cacheKey = '${userId}_${spot.id}';

    // 检查内存缓存
    if (useCache && _memoryCache[userId]?.containsKey(spot.id) == true) {
      debugPrint('🎯 [可见性] 使用内存缓存: ${spot.name}');
      return _memoryCache[userId]![spot.id]!;
    }

    // 检查本地缓存
    if (useCache) {
      final cachedResult = await _getFromLocalCache(cacheKey);
      if (cachedResult != null) {
        debugPrint('🎯 [可见性] 使用本地缓存: ${spot.name}');
        _updateMemoryCache(userId, spot.id, cachedResult);
        return cachedResult;
      }
    }

    // 执行实际的权限检查
    debugPrint(
      '🎯 [可见性] 执行权限检查: ${spot.name} (${spot.visibility.displayName})',
    );
    final hasAccess = await _checkSpotAccess(userId, spot);

    // 缓存结果
    if (useCache) {
      await _cacheResult(cacheKey, hasAccess);
      _updateMemoryCache(userId, spot.id, hasAccess);
    }

    debugPrint('🎯 [可见性] 权限检查结果: ${spot.name} = $hasAccess');
    return hasAccess;
  }

  /// 批量检查多个钓点的访问权限
  ///
  /// [userId] 用户ID
  /// [spots] 钓点列表
  ///
  /// 返回钓点ID到访问权限的映射
  Future<Map<String, bool>> checkMultipleSpotAccess(
    String userId,
    List<FishingSpot> spots,
  ) async {
    final results = <String, bool>{};
    final uncachedSpots = <FishingSpot>[];

    // 先检查缓存
    for (final spot in spots) {
      if (_memoryCache[userId]?.containsKey(spot.id) == true) {
        results[spot.id] = _memoryCache[userId]![spot.id]!;
      } else {
        uncachedSpots.add(spot);
      }
    }

    // 批量检查未缓存的钓点
    for (final spot in uncachedSpots) {
      final hasAccess = await _checkSpotAccess(userId, spot);
      results[spot.id] = hasAccess;
      _updateMemoryCache(userId, spot.id, hasAccess);
    }

    return results;
  }

  /// 执行实际的钓点访问权限检查
  Future<bool> _checkSpotAccess(String userId, FishingSpot spot) async {
    switch (spot.visibility) {
      case SpotVisibility.public:
        return true;

      case SpotVisibility.private:
        return spot.userId == userId;

      case SpotVisibility.friendsOnly:
        return await _checkFriendshipAccess(userId, spot.userId);

      case SpotVisibility.conditional:
        return await _checkConditionalAccess(userId, spot);
    }
  }

  /// 检查好友关系访问权限
  Future<bool> _checkFriendshipAccess(String userId, String spotOwnerId) async {
    // 钓点创建者总是可以访问
    if (userId == spotOwnerId) return true;

    try {
      // 检查是否关注了钓点创建者
      final records = await PocketBaseConfig.instance.client
          .collection('user_follows')
          .getList(
            filter: 'follower_id = "$userId" && following_id = "$spotOwnerId"',
            perPage: 1,
          );

      return records.items.isNotEmpty;
    } catch (e) {
      debugPrint('❌ [可见性] 检查友谊关系失败: $e');
      return false;
    }
  }

  /// 检查条件访问权限
  Future<bool> _checkConditionalAccess(String userId, FishingSpot spot) async {
    // 钓点创建者总是可以访问
    if (userId == spot.userId) return true;

    if (spot.visibilityConditions == null ||
        spot.visibilityConditions!.isEmpty) {
      debugPrint('⚠️ [可见性] 条件可见钓点没有设置条件: ${spot.name}');
      return false;
    }

    final conditions = spot.visibilityConditions!;
    final conditionType = conditions['type'] as String?;

    switch (conditionType) {
      case 'POINTS_DONATED':
        final minPoints = conditions['minPoints'] as int? ?? 0;
        return await _checkPointsDonated(userId, spot.userId, minPoints);

      case 'LEVEL_REQUIRED':
        final minLevel = conditions['minLevel'] as int? ?? 1;
        return await _checkUserLevel(userId, minLevel);

      case 'PAY_TO_VIEW':
        final price = conditions['price'] as int? ?? 0;
        return await _checkPayToView(userId, spot.userId, spot.id, price);

      default:
        debugPrint('⚠️ [可见性] 未知的条件类型: $conditionType');
        return false;
    }
  }

  /// 检查用户是否向钓点创建者赠送过足够积分
  Future<bool> _checkPointsDonated(
    String donorId,
    String recipientId,
    int minPoints,
  ) async {
    try {
      final records = await PocketBaseConfig.instance.client
          .collection('user_point_donations')
          .getList(
            filter: 'donor_id = "$donorId" && recipient_id = "$recipientId"',
          );

      final totalDonated = records.items.fold<int>(
        0,
        (sum, record) => sum + (record.data['points'] as int? ?? 0),
      );

      debugPrint(
        '🎯 [可见性] 积分检查: $donorId -> $recipientId, 已赠送: $totalDonated, 要求: $minPoints',
      );
      return totalDonated >= minPoints;
    } catch (e) {
      debugPrint('❌ [可见性] 检查积分赠送失败: $e');
      return false;
    }
  }

  /// 检查用户等级是否满足要求
  Future<bool> _checkUserLevel(String userId, int requiredLevel) async {
    try {
      final user = await Services.user.getUserById(userId);
      if (user == null) {
        debugPrint('❌ [可见性] 用户不存在: $userId');
        return false;
      }

      // 根据积分计算等级（示例逻辑：每1000积分为1级）
      final userLevel = (user.points / 1000).floor() + 1;

      debugPrint('🎯 [可见性] 等级检查: 用户等级 $userLevel, 要求等级 $requiredLevel');
      return userLevel >= requiredLevel;
    } catch (e) {
      debugPrint('❌ [可见性] 检查用户等级失败: $e');
      return false;
    }
  }

  /// 检查用户是否已支付查看费用
  Future<bool> _checkPayToView(
    String userId,
    String spotOwnerId,
    String spotId,
    int price,
  ) async {
    try {
      // 查询用户是否已经为这个钓点支付过费用
      final records = await PocketBaseConfig.instance.client
          .collection('user_point_donations')
          .getList(
            filter:
                'donor_id = "$userId" && recipient_id = "$spotOwnerId" && spot_id = "$spotId" && points >= $price',
            perPage: 1,
          );

      final hasPaid = records.items.isNotEmpty;
      debugPrint(
        '🎯 [可见性] 付费检查: 用户 $userId 对钓点 $spotId 的付费状态: $hasPaid (要求: $price 积分)',
      );
      return hasPaid;
    } catch (e) {
      debugPrint('❌ [可见性] 检查付费状态失败: $e');
      return false;
    }
  }

  /// 设置钓点可见性
  ///
  /// [spotId] 钓点ID
  /// [visibility] 可见性级别
  /// [conditions] 可见性条件（仅当visibility为conditional时需要）
  ///
  /// 返回是否设置成功
  Future<bool> setSpotVisibility(
    String spotId,
    SpotVisibility visibility, {
    Map<String, dynamic>? conditions,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'visibility': visibility.value,
        'visibility_updated_at': DateTime.now().toIso8601String(),
      };

      // 设置可见性条件
      if (visibility == SpotVisibility.conditional) {
        if (conditions == null || conditions.isEmpty) {
          throw Exception('条件可见性必须设置具体条件');
        }
        updateData['visibility_conditions'] = jsonEncode(conditions);
      } else {
        updateData['visibility_conditions'] = null;
      }

      await PocketBaseConfig.instance.client
          .collection('fishing_spots')
          .update(spotId, body: updateData);

      // 清除相关缓存
      _clearSpotCache(spotId);

      debugPrint('✅ [可见性] 钓点可见性设置成功: $spotId -> ${visibility.displayName}');
      return true;
    } catch (e) {
      debugPrint('❌ [可见性] 设置钓点可见性失败: $e');
      return false;
    }
  }

  /// 支付查看钓点费用
  ///
  /// [spotId] 钓点ID
  /// [spotOwnerId] 钓点创建者ID
  /// [price] 查看费用
  ///
  /// 返回是否支付成功
  Future<bool> payToViewSpot(
    String spotId,
    String spotOwnerId,
    int price,
  ) async {
    try {
      final currentUser = Services.user.currentUser;
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      if (currentUser.id == spotOwnerId) {
        // 钓点创建者不需要付费
        return true;
      }

      if (currentUser.points < price) {
        throw Exception('积分不足，需要 $price 积分，当前只有 ${currentUser.points} 积分');
      }

      // 检查是否已经支付过
      final alreadyPaid = await _checkPayToView(
        currentUser.id,
        spotOwnerId,
        spotId,
        price,
      );
      if (alreadyPaid) {
        debugPrint('✅ [可见性] 用户已支付过查看费用: $spotId');
        return true;
      }

      // 创建支付记录
      await PocketBaseConfig.instance.client
          .collection('user_point_donations')
          .create(
            body: {
              'donor_id': currentUser.id,
              'recipient_id': spotOwnerId,
              'points': price,
              'reason': '支付钓点查看费用',
              'spot_id': spotId,
            },
          );

      // 更新用户积分（这里需要调用用户服务的相关方法）
      // await Services.user.updateUserPoints(currentUser.id, -price);
      // await Services.user.updateUserPoints(spotOwnerId, price);

      // 清除相关的访问权限缓存
      _clearSpotCache(spotId);

      debugPrint(
        '✅ [可见性] 钓点查看费用支付成功: ${currentUser.id} -> $spotOwnerId, $price 积分, 钓点: $spotId',
      );
      return true;
    } catch (e) {
      debugPrint('❌ [可见性] 钓点查看费用支付失败: $e');
      return false;
    }
  }

  /// 赠送积分给用户
  ///
  /// [recipientId] 接收者用户ID
  /// [points] 积分数量
  /// [reason] 赠送原因
  /// [spotId] 关联的钓点ID（可选）
  ///
  /// 返回是否赠送成功
  Future<bool> donatePoints(
    String recipientId,
    int points, {
    String? reason,
    String? spotId,
  }) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      if (currentUser.points < points) {
        throw Exception('积分不足');
      }

      // 创建赠送记录
      await PocketBaseConfig.instance.client
          .collection('user_point_donations')
          .create(
            body: {
              'donor_id': currentUser.id,
              'recipient_id': recipientId,
              'points': points,
              'reason': reason,
              'spot_id': spotId,
            },
          );

      // 更新用户积分（这里需要调用用户服务的相关方法）
      // await Services.user.updateUserPoints(currentUser.id, -points);
      // await Services.user.updateUserPoints(recipientId, points);

      // 清除相关的访问权限缓存
      if (spotId != null) {
        _clearSpotCache(spotId);
      }

      debugPrint(
        '✅ [可见性] 积分赠送成功: ${currentUser.id} -> $recipientId, $points 积分',
      );
      return true;
    } catch (e) {
      debugPrint('❌ [可见性] 积分赠送失败: $e');
      return false;
    }
  }

  /// 获取用户的积分赠送记录
  Future<List<UserPointDonation>> getUserDonations(String userId) async {
    try {
      final records = await PocketBaseConfig.instance.client
          .collection('user_point_donations')
          .getList(filter: 'donor_id = "$userId"', sort: '-created');

      return records.items
          .map((record) => UserPointDonation.fromJson(record.toJson()))
          .toList();
    } catch (e) {
      debugPrint('❌ [可见性] 获取赠送记录失败: $e');
      return [];
    }
  }

  /// 清除访问权限缓存
  void clearAccessCache([String? userId]) {
    if (userId != null) {
      _memoryCache.remove(userId);
    } else {
      _memoryCache.clear();
    }
    debugPrint('🧹 [可见性] 清除访问权限缓存: ${userId ?? '全部'}');
  }

  /// 清除特定钓点的缓存
  void _clearSpotCache(String spotId) {
    _memoryCache.forEach((userId, spotCache) {
      spotCache.remove(spotId);
    });
  }

  /// 更新内存缓存
  void _updateMemoryCache(String userId, String spotId, bool hasAccess) {
    _memoryCache[userId] ??= {};
    _memoryCache[userId]![spotId] = hasAccess;
  }

  /// 从本地缓存获取结果
  Future<bool?> _getFromLocalCache(String cacheKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('$_cacheKeyPrefix$cacheKey');

      if (cachedData != null) {
        final data = jsonDecode(cachedData);
        final cachedTime = DateTime.parse(data['timestamp']);
        final expirationTime = cachedTime.add(
          const Duration(minutes: _cacheExpirationMinutes),
        );

        if (DateTime.now().isBefore(expirationTime)) {
          return data['result'] as bool;
        } else {
          // 缓存已过期，删除
          await prefs.remove('$_cacheKeyPrefix$cacheKey');
        }
      }
    } catch (e) {
      debugPrint('❌ [可见性] 读取本地缓存失败: $e');
    }

    return null;
  }

  /// 缓存结果到本地存储
  Future<void> _cacheResult(String cacheKey, bool result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'result': result,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await prefs.setString('$_cacheKeyPrefix$cacheKey', jsonEncode(cacheData));
    } catch (e) {
      debugPrint('❌ [可见性] 缓存结果失败: $e');
    }
  }
}
