import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../config/pocketbase_config.dart';
import '../models/user.dart';
import 'auth_service_new.dart';

/// 用户管理服务
///
/// 职责：
/// - 用户信息管理和CRUD操作
/// - 用户查询和搜索
/// - 用户数据缓存
/// - 用户统计信息
class UserService {
  // 单例模式
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  // 依赖的服务
  final AuthService _authService = AuthService();

  // 本地存储键
  static const String _usersStorageKey = 'users_cache';

  // 用户数据缓存
  List<User> _usersCache = [];
  DateTime _lastCacheTime = DateTime.now().subtract(const Duration(hours: 1));
  static const int _cacheDurationMinutes = 5;

  /// 获取当前登录用户
  User? get currentUser => _authService.currentUser;

  /// 监听当前用户变化
  ValueListenable<User?> get currentUserNotifier =>
      _authService.currentUserNotifier;

  /// 检查用户是否已登录
  bool get isLoggedIn => _authService.isLoggedIn;

  /// 初始化用户服务
  Future<void> initialize() async {
    debugPrint('初始化用户服务');
    await _loadUsersFromLocal();
  }

  /// 根据ID获取用户
  Future<User?> getUserById(String id) async {
    try {
      // 先从缓存查找
      final cachedUser = _usersCache.firstWhere(
        (user) => user.id == id,
        orElse: () => throw StateError('User not found in cache'),
      );

      // 如果缓存中有且不过期，直接返回
      if (_isCacheValid()) {
        return cachedUser;
      }
    } catch (e) {
      // 缓存中没有，继续从网络获取
    }

    try {
      final record = await pb.collection('users').getOne(id);
      final user = User.fromJson(record.toJson());

      // 更新缓存
      _updateUserInCache(user);

      return user;
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  /// 根据用户名获取用户
  Future<User?> getUserByUsername(String username) async {
    try {
      // 先从缓存查找
      if (_isCacheValid()) {
        try {
          final cachedUser = _usersCache.firstWhere(
            (user) => user.username == username,
          );
          return cachedUser;
        } catch (e) {
          // 缓存中没有，继续从网络获取
        }
      }

      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: 1, filter: 'username = "$username"');

      if (records.items.isNotEmpty) {
        final user = User.fromJson(records.items.first.toJson());
        _updateUserInCache(user);
        return user;
      }
      return null;
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  /// 搜索用户
  Future<List<User>> searchUsers(String query, {int limit = 20}) async {
    try {
      final records = await pb
          .collection('users')
          .getList(
            page: 1,
            perPage: limit,
            filter: 'username ~ "$query" || name ~ "$query"',
          );

      final users =
          records.items
              .map((record) => User.fromJson(record.toJson()))
              .toList();

      // 更新缓存
      for (final user in users) {
        _updateUserInCache(user);
      }

      return users;
    } catch (e) {
      debugPrint('搜索用户失败: $e');
      return [];
    }
  }

  /// 获取所有用户（带缓存）
  Future<List<User>> getAllUsers() async {
    // 检查缓存是否有效
    if (_usersCache.isNotEmpty && _isCacheValid()) {
      debugPrint('使用缓存的用户数据');
      return _usersCache;
    }

    try {
      final records = await pb.collection('users').getFullList();

      if (records.isEmpty) {
        debugPrint('API返回空用户列表');
        return _usersCache; // 返回缓存数据
      }

      // 更新缓存
      _usersCache =
          records.map((record) => User.fromJson(record.toJson())).toList();
      _lastCacheTime = DateTime.now();

      // 异步保存到本地
      _saveUsersToLocal().catchError((e) => debugPrint('保存用户数据失败: $e'));

      return _usersCache;
    } catch (e) {
      debugPrint('获取所有用户失败: $e');
      return _usersCache; // 返回缓存数据
    }
  }

  /// 更新用户信息
  Future<bool> updateUser(User user) async {
    try {
      // 检查权限：只能更新自己的信息
      if (currentUser?.id != user.id) {
        throw Exception('无权限更新其他用户信息');
      }

      // 更新 PocketBase 数据库
      await pb
          .collection('users')
          .update(
            user.id,
            body: {
              'username': user.username,
              'name': user.name,
              'bio': user.bio,
              'phone': user.phone,
              'avatar': user.avatar,
            },
          );

      // 更新缓存
      _updateUserInCache(user);

      debugPrint('用户信息更新成功: ${user.username}');
      return true;
    } catch (e) {
      debugPrint('更新用户信息失败: $e');
      return false;
    }
  }

  /// 更新用户头像
  Future<bool> updateUserAvatar(String userId, String avatarPath) async {
    try {
      // 检查权限
      if (currentUser?.id != userId) {
        throw Exception('无权限更新其他用户头像');
      }

      // TODO: 实现头像上传逻辑
      // 这里需要根据实际的文件上传需求来实现

      debugPrint('用户头像更新成功');
      return true;
    } catch (e) {
      debugPrint('更新用户头像失败: $e');
      return false;
    }
  }

  /// 获取用户统计信息
  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      // 获取用户发布的钓点数量
      final spotsCount = await pb
          .collection('fishing_spots')
          .getList(page: 1, perPage: 1, filter: 'user_id = "$userId"')
          .then((result) => result.totalItems);

      // 获取用户的关注数
      final followingCount = await pb
          .collection('user_follows')
          .getList(page: 1, perPage: 1, filter: 'follower_id = "$userId"')
          .then((result) => result.totalItems);

      // 获取用户的粉丝数
      final followersCount = await pb
          .collection('user_follows')
          .getList(page: 1, perPage: 1, filter: 'following_id = "$userId"')
          .then((result) => result.totalItems);

      // 获取用户的收藏数
      final favoritesCount = await pb
          .collection('user_favorites')
          .getList(page: 1, perPage: 1, filter: 'user_id = "$userId"')
          .then((result) => result.totalItems);

      return {
        'spots': spotsCount,
        'following': followingCount,
        'followers': followersCount,
        'favorites': favoritesCount,
      };
    } catch (e) {
      debugPrint('获取用户统计信息失败: $e');
      return {'spots': 0, 'following': 0, 'followers': 0, 'favorites': 0};
    }
  }

  /// 检查缓存是否有效
  bool _isCacheValid() {
    final now = DateTime.now();
    return now.difference(_lastCacheTime).inMinutes < _cacheDurationMinutes;
  }

  /// 更新缓存中的用户
  void _updateUserInCache(User user) {
    final index = _usersCache.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      _usersCache[index] = user;
    } else {
      _usersCache.add(user);
    }
  }

  /// 从本地存储加载用户数据
  Future<void> _loadUsersFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersStorageKey);

      if (usersJson != null) {
        final List<dynamic> decodedList = jsonDecode(usersJson);
        _usersCache = decodedList.map((item) => User.fromJson(item)).toList();
        debugPrint('从本地加载了 ${_usersCache.length} 个用户');
      }
    } catch (e) {
      debugPrint('从本地加载用户数据失败: $e');
      _usersCache = [];
    }
  }

  /// 保存用户数据到本地存储
  Future<void> _saveUsersToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = jsonEncode(
        _usersCache.map((user) => user.toJson()).toList(),
      );
      await prefs.setString(_usersStorageKey, usersJson);
      debugPrint('保存了 ${_usersCache.length} 个用户到本地');
    } catch (e) {
      debugPrint('保存用户数据到本地失败: $e');
    }
  }

  /// 清除缓存
  void clearCache() {
    _usersCache.clear();
    _lastCacheTime = DateTime.now().subtract(const Duration(hours: 1));
  }

  /// 刷新缓存
  Future<void> refreshCache() async {
    clearCache();
    await getAllUsers();
  }
}
