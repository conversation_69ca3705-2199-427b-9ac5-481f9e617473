import 'package:flutter/foundation.dart';
import 'auth_service_new.dart';
import 'user_service_new.dart';
import 'social_service.dart';
import 'fishing_spot_service_new.dart';
import 'location_service.dart';
import 'tile_cache_service.dart';
import 'secure_image_upload_service.dart';
import 'encrypted_r2_service.dart';
import 'spot_interaction_service.dart';
import 'spot_visibility_service.dart';

/// 服务定位器
///
/// 职责：
/// - 服务实例管理和注册
/// - 依赖注入
/// - 服务生命周期管理
/// - 提供统一的服务访问接口
class ServiceLocator {
  // 单例模式
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  // 服务实例存储
  final Map<Type, dynamic> _services = {};
  final Map<Type, bool> _initialized = {};

  /// 获取服务定位器实例
  static ServiceLocator get instance => _instance;

  // ==================== 服务注册 ====================

  /// 注册所有服务
  Future<void> registerServices() async {
    debugPrint('开始注册服务...');

    // 注册核心服务（按依赖顺序）
    _registerService<AuthService>(() => AuthService());
    _registerService<UserService>(() => UserService());
    _registerService<SocialService>(() => SocialService());
    _registerService<FishingSpotService>(() => FishingSpotService());

    // 注册其他服务
    _registerService<LocationService>(() => LocationService());
    _registerService<TileCacheService>(() => TileCacheService());
    _registerService<SpotInteractionService>(() => SpotInteractionService());
    _registerService<SpotVisibilityService>(() => SpotVisibilityService());

    // 注册安全上传相关服务
    _registerService<EncryptedR2Service>(() => EncryptedR2Service());
    _registerService<SecureImageUploadService>(
      () => SecureImageUploadService(),
    );

    debugPrint('服务注册完成');
  }

  /// 注册单个服务
  void _registerService<T>(T Function() factory) {
    if (!_services.containsKey(T)) {
      _services[T] = factory();
      _initialized[T] = false;
      debugPrint('注册服务: ${T.toString()}');
    }
  }

  // ==================== 服务获取 ====================

  /// 获取服务实例
  T get<T>() {
    if (!_services.containsKey(T)) {
      throw Exception('服务 ${T.toString()} 未注册');
    }
    return _services[T] as T;
  }

  /// 获取认证服务
  AuthService get authService => get<AuthService>();

  /// 获取用户服务
  UserService get userService => get<UserService>();

  /// 获取社交服务
  SocialService get socialService => get<SocialService>();

  /// 获取钓点服务
  FishingSpotService get fishingSpotService => get<FishingSpotService>();

  /// 获取位置服务
  LocationService get locationService => get<LocationService>();

  /// 获取缓存服务
  TileCacheService get cacheService => get<TileCacheService>();

  /// 获取加密R2服务
  EncryptedR2Service get encryptedR2Service => get<EncryptedR2Service>();

  /// 获取安全图片上传服务
  SecureImageUploadService get secureUploadService =>
      get<SecureImageUploadService>();

  /// 获取钓点互动服务
  SpotInteractionService get spotInteractionService =>
      get<SpotInteractionService>();

  /// 获取钓点可见性服务
  SpotVisibilityService get spotVisibilityService =>
      get<SpotVisibilityService>();

  // ==================== 服务初始化 ====================

  /// 初始化所有服务
  Future<void> initializeServices() async {
    debugPrint('开始初始化服务...');

    try {
      // 按依赖顺序初始化服务
      await _initializeService<AuthService>();
      await _initializeService<UserService>();
      await _initializeService<SocialService>();
      await _initializeService<FishingSpotService>();
      await _initializeService<LocationService>();
      await _initializeService<TileCacheService>();

      debugPrint('所有服务初始化完成');
    } catch (e) {
      debugPrint('服务初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化单个服务
  Future<void> _initializeService<T>() async {
    if (_initialized[T] == true) {
      debugPrint('服务 ${T.toString()} 已经初始化');
      return;
    }

    try {
      final service = get<T>();

      // 调用服务的初始化方法（如果存在）
      if (service is AuthService) {
        await (service as AuthService).initialize();
      } else if (service is UserService) {
        await (service as UserService).initialize();
      } else if (service is SocialService) {
        await (service as SocialService).initialize();
      } else if (service is FishingSpotService) {
        await (service as FishingSpotService).initialize();
      } else if (service is LocationService) {
        await (service as LocationService).initialize();
      } else if (service is TileCacheService) {
        await (service as TileCacheService).initialize();
      }

      _initialized[T] = true;
      debugPrint('服务 ${T.toString()} 初始化完成');
    } catch (e) {
      debugPrint('服务 ${T.toString()} 初始化失败: $e');
      rethrow;
    }
  }

  // ==================== 服务状态管理 ====================

  /// 检查服务是否已注册
  bool isRegistered<T>() {
    return _services.containsKey(T);
  }

  /// 检查服务是否已初始化
  bool isInitialized<T>() {
    return _initialized[T] == true;
  }

  /// 获取所有已注册的服务类型
  List<Type> getRegisteredServices() {
    return _services.keys.toList();
  }

  /// 获取所有已初始化的服务类型
  List<Type> getInitializedServices() {
    return _initialized.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();
  }

  // ==================== 服务重置和清理 ====================

  /// 重置特定服务
  Future<void> resetService<T>() async {
    if (_services.containsKey(T)) {
      _initialized[T] = false;

      // 如果服务有dispose方法，调用它
      final service = _services[T];
      if (service is AuthService) {
        service.dispose();
      }

      // 重新创建服务实例
      if (T == AuthService) {
        _services[T] = AuthService();
      } else if (T == UserService) {
        _services[T] = UserService();
      } else if (T == SocialService) {
        _services[T] = SocialService();
      } else if (T == FishingSpotService) {
        _services[T] = FishingSpotService();
      } else if (T == LocationService) {
        _services[T] = LocationService();
      } else if (T == TileCacheService) {
        _services[T] = TileCacheService();
      }

      // 重新初始化
      await _initializeService<T>();

      debugPrint('服务 ${T.toString()} 已重置');
    }
  }

  /// 清理所有服务
  void dispose() {
    debugPrint('开始清理服务...');

    // 调用所有服务的dispose方法
    for (final service in _services.values) {
      try {
        if (service is AuthService) {
          service.dispose();
        }
        // 其他服务如果有dispose方法也在这里调用
      } catch (e) {
        debugPrint('清理服务时出错: $e');
      }
    }

    _services.clear();
    _initialized.clear();

    debugPrint('服务清理完成');
  }

  // ==================== 调试和监控 ====================

  /// 打印服务状态
  void printServiceStatus() {
    if (!kDebugMode) return;

    debugPrint('=== 服务状态 ===');
    debugPrint('已注册服务数量: ${_services.length}');
    debugPrint('已初始化服务数量: ${getInitializedServices().length}');

    for (final type in _services.keys) {
      final initialized = _initialized[type] == true ? '✅' : '❌';
      debugPrint('$initialized ${type.toString()}');
    }
    debugPrint('===============');
  }

  /// 获取服务健康状态
  Map<String, dynamic> getServiceHealth() {
    final health = <String, dynamic>{};

    for (final type in _services.keys) {
      health[type.toString()] = {
        'registered': true,
        'initialized': _initialized[type] == true,
        'instance': _services[type] != null,
      };
    }

    return health;
  }
}

// ==================== 便捷访问器 ====================

/// 全局服务定位器实例
final serviceLocator = ServiceLocator.instance;

/// 便捷的服务访问器
class Services {
  static AuthService get auth => serviceLocator.authService;
  static UserService get user => serviceLocator.userService;
  static SocialService get social => serviceLocator.socialService;
  static FishingSpotService get fishingSpot =>
      serviceLocator.fishingSpotService;
  static LocationService get location => serviceLocator.locationService;
  static TileCacheService get cache => serviceLocator.cacheService;
  static EncryptedR2Service get encryptedR2 =>
      serviceLocator.encryptedR2Service;
  static SecureImageUploadService get secureUpload =>
      serviceLocator.secureUploadService;
  static SpotInteractionService get spotInteraction =>
      serviceLocator.spotInteractionService;
  static SpotVisibilityService get spotVisibility =>
      serviceLocator.spotVisibilityService;
}
