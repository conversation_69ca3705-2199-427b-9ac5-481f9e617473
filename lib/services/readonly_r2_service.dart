import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';

/// 只读R2服务
/// 使用固定的只读凭据生成预签名URL
class ReadonlyR2Service {
  // 只读凭据（来自key.md文件）
  static const String _accessKeyId = '9ce7c7d0cde4eedeee735ddc2dc6a225';
  static const String _secretAccessKey = '****************************************************************';
  static const String _endpoint = 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com';
  static const String _bucketName = 'fishing-app';
  static const String _region = 'auto';

  /// 生成预签名URL用于读取图片
  Future<String?> generatePresignedUrl({
    required String objectKey,
    int expiresIn = 3600, // 1小时有效期
  }) async {
    try {
      debugPrint('🔍 [只读R2] 开始生成预签名URL');
      debugPrint('🔍 [只读R2] 对象键: $objectKey');
      debugPrint('🔍 [只读R2] 有效期: ${expiresIn}秒');

      final now = DateTime.now().toUtc();
      final timestamp = _formatTimestamp(now);
      final dateStamp = _formatDateStamp(now);
      
      debugPrint('🔍 [只读R2] 时间戳: $timestamp');
      debugPrint('🔍 [只读R2] 日期戳: $dateStamp');

      // 构建查询参数
      final queryParams = <String, String>{
        'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
        'X-Amz-Credential': '$_accessKeyId/$dateStamp/$_region/s3/aws4_request',
        'X-Amz-Date': timestamp,
        'X-Amz-Expires': expiresIn.toString(),
        'X-Amz-SignedHeaders': 'host',
      };

      // 构建规范请求
      final canonicalRequest = _buildCanonicalRequest(
        method: 'GET',
        objectKey: objectKey,
        queryParams: queryParams,
      );

      debugPrint('🔍 [只读R2] 规范请求构建完成');

      // 构建待签名字符串
      final stringToSign = _buildStringToSign(
        timestamp: timestamp,
        dateStamp: dateStamp,
        canonicalRequest: canonicalRequest,
      );

      debugPrint('🔍 [只读R2] 待签名字符串构建完成');

      // 计算签名
      final signature = _calculateSignature(
        secretKey: _secretAccessKey,
        dateStamp: dateStamp,
        stringToSign: stringToSign,
      );

      debugPrint('🔍 [只读R2] 签名计算完成: $signature');

      // 添加签名到查询参数
      queryParams['X-Amz-Signature'] = signature;

      // 构建最终URL
      final uri = Uri.https(
        Uri.parse(_endpoint).host,
        '/$_bucketName/$objectKey',
        queryParams,
      );

      final presignedUrl = uri.toString();
      debugPrint('✅ [只读R2] 预签名URL生成成功');
      
      return presignedUrl;
    } catch (e) {
      debugPrint('❌ [只读R2] 生成预签名URL失败: $e');
      return null;
    }
  }

  /// 格式化时间戳
  String _formatTimestamp(DateTime dateTime) {
    return dateTime.toIso8601String()
        .replaceAll('-', '')
        .replaceAll(':', '')
        .replaceAll('.', '')
        .substring(0, 15) + 'Z';
  }

  /// 格式化日期戳
  String _formatDateStamp(DateTime dateTime) {
    return dateTime.toIso8601String()
        .replaceAll('-', '')
        .substring(0, 8);
  }

  /// 构建规范请求
  String _buildCanonicalRequest({
    required String method,
    required String objectKey,
    required Map<String, String> queryParams,
  }) {
    // 规范URI
    final canonicalUri = '/$_bucketName/$objectKey';

    // 规范查询字符串
    final sortedParams = Map.fromEntries(
      queryParams.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );
    final canonicalQueryString = sortedParams.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    // 规范头部
    final canonicalHeaders = 'host:${Uri.parse(_endpoint).host}\n';

    // 签名头部
    const signedHeaders = 'host';

    // 负载哈希
    const payloadHash = 'UNSIGNED-PAYLOAD';

    final canonicalRequest = [
      method,
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      payloadHash,
    ].join('\n');

    return canonicalRequest;
  }

  /// 构建待签名字符串
  String _buildStringToSign({
    required String timestamp,
    required String dateStamp,
    required String canonicalRequest,
  }) {
    const algorithm = 'AWS4-HMAC-SHA256';
    final credentialScope = '$dateStamp/$_region/s3/aws4_request';
    final hashedCanonicalRequest = sha256.convert(utf8.encode(canonicalRequest)).toString();

    return [
      algorithm,
      timestamp,
      credentialScope,
      hashedCanonicalRequest,
    ].join('\n');
  }

  /// 计算签名
  String _calculateSignature({
    required String secretKey,
    required String dateStamp,
    required String stringToSign,
  }) {
    // AWS4 签名计算
    final kDate = _hmacSha256(utf8.encode('AWS4$secretKey'), utf8.encode(dateStamp));
    final kRegion = _hmacSha256(kDate, utf8.encode(_region));
    final kService = _hmacSha256(kRegion, utf8.encode('s3'));
    final kSigning = _hmacSha256(kService, utf8.encode('aws4_request'));
    final signature = _hmacSha256(kSigning, utf8.encode(stringToSign));

    return signature.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
  }

  /// HMAC-SHA256计算
  List<int> _hmacSha256(List<int> key, List<int> data) {
    final hmac = Hmac(sha256, key);
    return hmac.convert(data).bytes;
  }
}
