import 'dart:convert';
import 'package:latlong2/latlong.dart';
import 'spot_comment.dart';
import 'emoji_marker.dart';
import 'spot_visibility.dart';

/// 钓点模型类
///
/// 遵循 PocketBase 标准字段命名规范：
/// - 使用 PocketBase 内置的 id, created, updated 字段
/// - 地理位置使用单独的 latitude, longitude 字段
/// - 关系数据通过单独的集合管理
class FishingSpot {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 钓点名称 (必填)
  final String name;

  /// 描述信息 (可选)
  String description;

  /// 纬度
  final double latitude;

  /// 经度
  final double longitude;

  /// 创建者用户ID (关联到 users 集合)
  final String userId;

  /// 地址信息 (可选)
  String? address;

  /// 钓点类型 (如：淡水、海水、池塘等)
  String? spotType;

  /// 鱼类信息 (可选)
  String? fishTypes;

  /// 钓点标记emoji (可选)
  String? spotEmoji;

  /// 鱼类标记emoji (可选)
  String? fishEmoji;

  /// 是否公开 (默认true) - 保留向后兼容
  bool isPublic;

  /// 状态 (active, inactive, reported)
  String status;

  /// 可见性级别
  final SpotVisibility visibility;

  /// 可见性条件（JSON格式）
  final Map<String, dynamic>? visibilityConditions;

  /// 可见性更新时间
  final DateTime visibilityUpdatedAt;

  /// 点赞数（冗余存储，提高查询性能）
  final int likesCount;

  /// 点赞数最后更新时间
  final DateTime likesCountUpdatedAt;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  FishingSpot({
    required this.id,
    required this.name,
    this.description = '',
    required this.latitude,
    required this.longitude,
    required this.userId,
    this.address,
    this.spotType,
    this.fishTypes,
    this.spotEmoji,
    this.fishEmoji,
    this.isPublic = true,
    this.status = 'active',
    this.likesCount = 0,
    DateTime? likesCountUpdatedAt,
    required this.created,
    required this.updated,
    this.visibility = SpotVisibility.public,
    this.visibilityConditions,
    DateTime? visibilityUpdatedAt,
  }) : likesCountUpdatedAt = likesCountUpdatedAt ?? DateTime.now(),
       visibilityUpdatedAt = visibilityUpdatedAt ?? DateTime.now();

  /// 从JSON创建钓点对象
  factory FishingSpot.fromJson(Map<String, dynamic> json) {
    return FishingSpot(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      userId: json['user_id'],
      address: json['address'],
      spotType: json['spot_type'],
      fishTypes: json['fish_types'],
      spotEmoji: json['spot_emoji'],
      fishEmoji: json['fish_emoji'],
      isPublic: json['is_public'] ?? true,
      status: json['status'] ?? 'active',
      likesCount: json['likes_count'] ?? 0,
      likesCountUpdatedAt:
          json['likes_count_updated_at'] != null
              ? DateTime.parse(json['likes_count_updated_at'])
              : DateTime.now(),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
      visibility: SpotVisibility.fromString(json['visibility'] ?? 'PUBLIC'),
      visibilityConditions:
          json['visibility_conditions'] != null
              ? (json['visibility_conditions'] is String
                  ? jsonDecode(json['visibility_conditions'])
                  : json['visibility_conditions'])
              : null,
      visibilityUpdatedAt:
          json['visibility_updated_at'] != null
              ? DateTime.parse(json['visibility_updated_at'])
              : DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'user_id': userId,
      'address': address,
      'spot_type': spotType,
      'fish_types': fishTypes,
      'spot_emoji': spotEmoji,
      'fish_emoji': fishEmoji,
      'is_public': isPublic,
      'status': status,
      'likes_count': likesCount,
      'likes_count_updated_at': likesCountUpdatedAt.toIso8601String(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'visibility': visibility.value,
      'visibility_conditions':
          visibilityConditions != null
              ? jsonEncode(visibilityConditions)
              : null,
      'visibility_updated_at': visibilityUpdatedAt.toIso8601String(),
    };
  }

  /// 兼容性方法：获取位置对象（兼容旧代码）
  LatLng get location => LatLng(latitude, longitude);

  /// 兼容性方法：获取分享者（兼容旧代码）
  String get sharedBy => userId; // 临时返回用户ID，实际应该通过关联查询获取用户名

  /// 兼容性方法：获取创建时间（兼容旧代码）
  DateTime get createdAt => created;

  /// 兼容性方法：获取更新时间（兼容旧代码）
  DateTime get updatedAt => updated;

  /// 兼容性方法：点赞数（现在直接从冗余字段获取）
  int get likes => likesCount;

  /// 兼容性方法：不喜欢数（通过单独的集合管理）
  int get unlikes => 0; // 临时返回0，实际应该通过关联查询获取

  /// 兼容性方法：评论列表（通过单独的集合管理）
  List<Comment> get comments => []; // 临时返回空列表，实际应该通过关联查询获取

  /// 兼容性方法：照片URL列表（通过单独的集合管理）
  List<String> get photoUrls => []; // 临时返回空列表，实际应该通过关联查询获取

  /// 兼容性方法：全景照片URL（通过单独的集合管理）
  String? get panoramaPhotoUrl => null; // 临时返回null，实际应该通过关联查询获取

  /// 获取钓点显示的emoji（优先使用自定义emoji，否则根据类型自动选择）
  String get displayEmoji {
    if (spotEmoji != null && spotEmoji!.isNotEmpty) {
      return spotEmoji!;
    }
    return FishingSpotMarkers.getSpotTypeEmoji(spotType);
  }

  /// 获取鱼类显示的emoji（优先使用自定义emoji，否则根据类型自动选择）
  String get displayFishEmoji {
    if (fishEmoji != null && fishEmoji!.isNotEmpty) {
      return fishEmoji!;
    }
    return FishingSpotMarkers.getFishTypeEmoji(fishTypes);
  }
}
