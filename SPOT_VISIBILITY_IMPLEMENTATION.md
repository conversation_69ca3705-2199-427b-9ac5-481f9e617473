# 钓点可见性功能 - 实现指南

## 🚀 实施步骤

### 第一阶段：数据库迁移

1. **扩展钓点表**
```sql
-- 添加可见性字段
ALTER TABLE fishing_spots ADD COLUMN visibility TEXT DEFAULT 'PUBLIC';
ALTER TABLE fishing_spots ADD COLUMN visibility_conditions TEXT;
ALTER TABLE fishing_spots ADD COLUMN visibility_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- 创建索引提高查询性能
CREATE INDEX idx_fishing_spots_visibility ON fishing_spots(visibility);
CREATE INDEX idx_fishing_spots_user_visibility ON fishing_spots(user_id, visibility);
```

2. **创建相关表**
```sql
-- 用户积分赠送记录表
CREATE TABLE user_point_donations (
    id TEXT PRIMARY KEY,
    donor_id TEXT NOT NULL,
    recipient_id TEXT NOT NULL,
    points INTEGER NOT NULL,
    reason TEXT,
    spot_id TEXT,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (donor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (spot_id) REFERENCES fishing_spots(id) ON DELETE SET NULL
);

-- 访问权限缓存表（可选，用于性能优化）
CREATE TABLE spot_access_cache (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    spot_id TEXT NOT NULL,
    has_access BOOLEAN NOT NULL,
    access_reason TEXT,
    expires_at DATETIME,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (spot_id) REFERENCES fishing_spots(id) ON DELETE CASCADE,
    UNIQUE(user_id, spot_id)
);

-- 创建索引
CREATE INDEX idx_point_donations_donor ON user_point_donations(donor_id);
CREATE INDEX idx_point_donations_recipient ON user_point_donations(recipient_id);
CREATE INDEX idx_access_cache_user_spot ON spot_access_cache(user_id, spot_id);
```

### 第二阶段：后端Hook实现

1. **创建钓点过滤Hook**
```bash
# 在 pb_hooks 目录下创建文件
touch pb_hooks/fishing_spots_visibility.pb.js
```

2. **实现可见性验证Hook**
```bash
touch pb_hooks/spot_visibility_validation.pb.js
```

### 第三阶段：前端模型扩展

1. **更新FishingSpot模型**
```dart
// 在 lib/models/fishing_spot.dart 中添加字段
final SpotVisibility visibility;
final Map<String, dynamic>? visibilityConditions;
final DateTime visibilityUpdatedAt;
```

2. **创建可见性相关模型**
```bash
touch lib/models/spot_visibility.dart
touch lib/models/visibility_condition.dart
```

### 第四阶段：服务层实现

1. **创建可见性服务**
```bash
touch lib/services/spot_visibility_service.dart
```

2. **扩展钓点服务**
```dart
// 在 FishingSpotService 中添加可见性相关方法
Future<List<FishingSpot>> getVisibleSpots();
Future<bool> setSpotVisibility();
```

### 第五阶段：UI组件实现

1. **创建可见性设置组件**
```bash
touch lib/widgets/spot_visibility_selector.dart
touch lib/widgets/visibility_condition_editor.dart
```

2. **更新钓点创建/编辑页面**
```dart
// 在钓点创建页面中集成可见性设置
SpotVisibilitySelector(
  onChanged: (visibility, conditions) {
    // 处理可见性变更
  },
)
```

## 🔒 安全考虑

### 1. 权限验证
- 只有钓点创建者可以修改可见性设置
- 后端必须验证用户身份和权限
- 防止通过API直接绕过可见性限制

### 2. 数据验证
- 验证可见性条件的合法性
- 防止恶意设置过高的积分要求
- 限制条件可见性的复杂度

### 3. 性能优化
- 使用缓存减少重复的权限检查
- 在数据库层面进行初步过滤
- 合理设置缓存过期时间

## 📊 性能优化策略

### 1. 数据库优化
```sql
-- 创建复合索引
CREATE INDEX idx_spots_visibility_user ON fishing_spots(visibility, user_id);
CREATE INDEX idx_spots_location_visibility ON fishing_spots(latitude, longitude, visibility);

-- 分区表（如果数据量很大）
-- 按可见性类型分区可以提高查询效率
```

### 2. 缓存策略
```dart
// 多层缓存
class SpotVisibilityService {
  // 内存缓存 - 最近访问的权限结果
  final Map<String, bool> _memoryCache = {};
  
  // 本地存储缓存 - 持久化权限结果
  Future<void> _cacheToLocal(String key, bool result) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, result);
  }
  
  // 数据库缓存 - spot_access_cache 表
  Future<void> _cacheToDatabase(String userId, String spotId, bool hasAccess) async {
    // 实现数据库缓存逻辑
  }
}
```

### 3. 批量处理
```dart
// 批量检查多个钓点的访问权限
Future<Map<String, bool>> checkMultipleSpotAccess(
  String userId, 
  List<String> spotIds,
) async {
  final results = <String, bool>{};
  
  // 批量查询，减少数据库访问次数
  final cachedResults = await _batchGetFromCache(userId, spotIds);
  final uncachedSpotIds = spotIds.where((id) => !cachedResults.containsKey(id)).toList();
  
  if (uncachedSpotIds.isNotEmpty) {
    final freshResults = await _batchCheckAccess(userId, uncachedSpotIds);
    results.addAll(freshResults);
    
    // 批量缓存结果
    await _batchCacheResults(userId, freshResults);
  }
  
  results.addAll(cachedResults);
  return results;
}
```

## 🧪 测试策略

### 1. 单元测试
```dart
// test/services/spot_visibility_service_test.dart
void main() {
  group('SpotVisibilityService', () {
    test('should allow owner to view private spot', () async {
      // 测试钓点创建者可以查看私有钓点
    });
    
    test('should deny non-friend access to friends-only spot', () async {
      // 测试非好友无法查看好友可见钓点
    });
    
    test('should allow access when conditions are met', () async {
      // 测试满足条件时可以访问条件可见钓点
    });
  });
}
```

### 2. 集成测试
```dart
// integration_test/spot_visibility_test.dart
void main() {
  group('Spot Visibility Integration', () {
    testWidgets('should filter spots based on visibility', (tester) async {
      // 测试钓点列表根据可见性正确过滤
    });
    
    testWidgets('should update visibility settings', (tester) async {
      // 测试可见性设置更新功能
    });
  });
}
```

### 3. 性能测试
```dart
void main() {
  test('should handle large number of spots efficiently', () async {
    // 测试大量钓点的可见性检查性能
    final stopwatch = Stopwatch()..start();
    
    await spotVisibilityService.checkMultipleSpotAccess(userId, spotIds);
    
    stopwatch.stop();
    expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // 应在1秒内完成
  });
}
```

## 📱 用户体验优化

### 1. 渐进式加载
```dart
// 优先加载公开钓点，然后异步加载其他可见钓点
Future<void> loadSpotsProgressively() async {
  // 1. 立即显示公开钓点
  final publicSpots = await getPublicSpots();
  setState(() => spots.addAll(publicSpots));
  
  // 2. 异步加载好友钓点
  final friendSpots = await getFriendSpots();
  setState(() => spots.addAll(friendSpots));
  
  // 3. 最后加载条件可见钓点
  final conditionalSpots = await getConditionalSpots();
  setState(() => spots.addAll(conditionalSpots));
}
```

### 2. 智能提示
```dart
// 当用户无法查看某个钓点时，提供友好的提示
Widget buildSpotAccessHint(FishingSpot spot) {
  switch (spot.visibility) {
    case SpotVisibility.friendsOnly:
      return const Text('关注作者后可查看此钓点');
    case SpotVisibility.conditional:
      return Text('${_getConditionHint(spot.visibilityConditions)}后可查看');
    default:
      return const SizedBox.shrink();
  }
}
```

### 3. 可见性状态指示
```dart
// 在钓点卡片上显示可见性状态
Widget buildVisibilityIndicator(SpotVisibility visibility) {
  switch (visibility) {
    case SpotVisibility.private:
      return const Icon(Icons.lock, color: Colors.red, size: 16);
    case SpotVisibility.friendsOnly:
      return const Icon(Icons.people, color: Colors.orange, size: 16);
    case SpotVisibility.conditional:
      return const Icon(Icons.star, color: Colors.blue, size: 16);
    case SpotVisibility.public:
      return const Icon(Icons.public, color: Colors.green, size: 16);
  }
}
```
