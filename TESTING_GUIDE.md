# 钓点详情页面功能测试指南

## 前置条件

### 1. 数据库设置
请先按照 `database_setup.md` 文件的指导完成数据库表的创建：
- 修改 `fishing_spots` 表，添加 `likes` 和 `unlikes` 字段
- 创建 `user_likes` 表
- 创建 `user_unlikes` 表

### 2. 用户账户
确保有可用的用户账户进行测试。

## 测试步骤

### 1. 启动应用
```bash
cd /home/<USER>/otherWS/yuwuzi/fishing_app
flutter run --debug
```

### 2. 登录测试
1. 启动应用后登录用户账户
2. 确保登录成功，能看到地图界面

### 3. 图片加载测试

#### 测试步骤：
1. 在地图上点击任意钓点
2. 观察钓点详情页面是否正常弹出
3. 检查钓点照片是否能正常加载显示

#### 预期结果：
- ✅ 钓点详情页面正常弹出
- ✅ 照片能正常加载和显示
- ✅ 照片轮播功能正常
- ✅ 点击照片能进入全屏查看模式
- ✅ 照片缩略图网格正常显示

#### 如果图片加载失败：
检查控制台输出，应该能看到类似以下的日志：
```
🔍 [签名URL] 提取的对象键: spots/xxx/photo.jpg
✅ [签名URL] 生成成功
```

### 4. 点赞功能测试

#### 测试步骤：
1. 在钓点详情页面底部找到点赞按钮（👍图标）
2. 点击点赞按钮
3. 观察按钮状态变化
4. 再次点击点赞按钮（取消点赞）
5. 观察按钮状态变化

#### 预期结果：
- ✅ 首次点击后，点赞按钮变为蓝色（已点赞状态）
- ✅ 点赞数字增加1
- ✅ 再次点击后，点赞按钮变为灰色（未点赞状态）
- ✅ 点赞数字减少1

#### 控制台日志：
```
🔍 [互动加载] 开始加载用户互动记录: user_id
✅ [互动加载] 加载完成: 点赞0个, 倒赞0个
🔍 [点赞] 开始点赞钓点: spot_id, 用户: user_id
✅ [点赞] 点赞成功
```

### 5. 倒赞功能测试

#### 测试步骤：
1. 在钓点详情页面底部找到倒赞按钮（👎图标）
2. 点击倒赞按钮
3. 观察按钮状态变化
4. 再次点击倒赞按钮（取消倒赞）
5. 观察按钮状态变化

#### 预期结果：
- ✅ 首次点击后，倒赞按钮变为红色（已倒赞状态）
- ✅ 倒赞数字增加1
- ✅ 再次点击后，倒赞按钮变为灰色（未倒赞状态）
- ✅ 倒赞数字减少1

### 6. 点赞倒赞互斥测试

#### 测试步骤：
1. 先点击点赞按钮（确保处于点赞状态）
2. 然后点击倒赞按钮
3. 观察两个按钮的状态变化

#### 预期结果：
- ✅ 点击倒赞后，点赞按钮自动变为未点赞状态
- ✅ 倒赞按钮变为已倒赞状态
- ✅ 点赞数减少1，倒赞数增加1

### 7. 数据持久性测试

#### 测试步骤：
1. 对某个钓点进行点赞操作
2. 关闭钓点详情页面
3. 重新打开同一个钓点的详情页面
4. 检查点赞状态是否保持

#### 预期结果：
- ✅ 重新打开后，点赞状态正确显示
- ✅ 点赞数字正确显示

### 8. 多用户测试（如果有多个账户）

#### 测试步骤：
1. 用户A对钓点进行点赞
2. 切换到用户B账户
3. 查看同一钓点的详情
4. 用户B也进行点赞操作

#### 预期结果：
- ✅ 用户B能看到钓点的点赞数已经增加
- ✅ 用户B的点赞操作独立于用户A

## 常见问题排查

### 问题1：图片无法加载
**可能原因**：
- 只读R2服务配置问题
- 网络连接问题
- 图片URL格式问题

**排查方法**：
1. 检查控制台是否有签名URL生成的日志
2. 检查 `key.md` 文件中的只读key是否正确
3. 检查网络连接

### 问题2：点赞功能无响应
**可能原因**：
- 数据库表未正确创建
- 用户未登录
- 权限配置问题

**排查方法**：
1. 检查控制台错误日志
2. 确认用户已登录
3. 检查PocketBase管理界面中的表结构

### 问题3："Missing collection context" 错误
**可能原因**：
- 数据库表名拼写错误
- 表未正确创建

**排查方法**：
1. 登录PocketBase管理界面确认表是否存在
2. 检查表名拼写是否正确

### 问题4：权限错误
**可能原因**：
- API Rules配置不正确
- 用户未登录

**排查方法**：
1. 检查表的API Rules设置
2. 确认用户登录状态

## 性能测试

### 网络请求优化验证
1. 打开多个钓点详情页面
2. 观察控制台日志
3. 确认用户互动记录只加载一次

#### 预期结果：
```
🔍 [互动加载] 开始加载用户互动记录: user_id
✅ [互动加载] 加载完成: 点赞5个, 倒赞2个
✅ [互动加载] 使用缓存的互动数据  // 后续请求应该显示这个
```

## 测试完成标准

所有以下功能都正常工作：
- ✅ 图片正常加载显示
- ✅ 点赞功能正常
- ✅ 倒赞功能正常
- ✅ 点赞倒赞互斥逻辑正常
- ✅ 数据持久化正常
- ✅ 用户权限控制正常
- ✅ 网络请求优化生效
- ✅ 无严重错误日志

完成测试后，钓点详情页面的图片加载和点赞功能就可以正常使用了！
