# 钓点可见性功能设计方案

## 🎯 功能需求

支持4种钓点可见性级别：
1. **私有（PRIVATE）** - 只有创建者可见
2. **条件可见（CONDITIONAL）** - 符合特定条件的用户可见
3. **好友可见（FRIENDS_ONLY）** - 创建者的好友可见  
4. **完全公开（PUBLIC）** - 所有用户可见

## 🏗️ 数据库设计

### 1. 钓点表扩展（fishing_spots）

```sql
-- 新增字段
ALTER TABLE fishing_spots ADD COLUMN visibility TEXT DEFAULT 'PUBLIC';
ALTER TABLE fishing_spots ADD COLUMN visibility_conditions TEXT; -- JSON格式存储条件
ALTER TABLE fishing_spots ADD COLUMN visibility_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- 可见性枚举值
-- 'PUBLIC': 完全公开
-- 'FRIENDS_ONLY': 好友可见
-- 'CONDITIONAL': 条件可见
-- 'PRIVATE': 私有
```

### 2. 新增相关表

```sql
-- 钓点可见性条件表
CREATE TABLE spot_visibility_conditions (
    id TEXT PRIMARY KEY,
    spot_id TEXT NOT NULL,
    condition_type TEXT NOT NULL, -- 'POINTS_DONATED', 'LEVEL_REQUIRED', 'CUSTOM'
    condition_value TEXT NOT NULL, -- JSON格式存储具体条件
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (spot_id) REFERENCES fishing_spots(id) ON DELETE CASCADE
);

-- 用户积分赠送记录表（如果不存在）
CREATE TABLE user_point_donations (
    id TEXT PRIMARY KEY,
    donor_id TEXT NOT NULL,
    recipient_id TEXT NOT NULL,
    points INTEGER NOT NULL,
    reason TEXT,
    spot_id TEXT, -- 可选，关联到特定钓点
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (donor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (spot_id) REFERENCES fishing_spots(id) ON DELETE SET NULL
);

-- 钓点访问权限缓存表（性能优化）
CREATE TABLE spot_access_cache (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    spot_id TEXT NOT NULL,
    has_access BOOLEAN NOT NULL,
    access_reason TEXT, -- 'OWNER', 'FRIEND', 'CONDITION_MET', 'PUBLIC'
    expires_at DATETIME,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (spot_id) REFERENCES fishing_spots(id) ON DELETE CASCADE,
    UNIQUE(user_id, spot_id)
);
```

## 📱 前端实现

### 1. 数据模型扩展

```dart
// lib/models/spot_visibility.dart
enum SpotVisibility {
  private('PRIVATE', '私有'),
  friendsOnly('FRIENDS_ONLY', '好友可见'),
  conditional('CONDITIONAL', '条件可见'),
  public('PUBLIC', '完全公开');

  const SpotVisibility(this.value, this.displayName);
  final String value;
  final String displayName;

  static SpotVisibility fromString(String value) {
    return SpotVisibility.values.firstWhere(
      (v) => v.value == value,
      orElse: () => SpotVisibility.public,
    );
  }
}

// 可见性条件类型
enum VisibilityConditionType {
  pointsDonated('POINTS_DONATED', '积分赠送'),
  levelRequired('LEVEL_REQUIRED', '等级要求'),
  custom('CUSTOM', '自定义条件');

  const VisibilityConditionType(this.value, this.displayName);
  final String value;
  final String displayName;
}

// 可见性条件模型
class SpotVisibilityCondition {
  final String id;
  final String spotId;
  final VisibilityConditionType type;
  final Map<String, dynamic> value;
  final DateTime created;
  final DateTime updated;

  SpotVisibilityCondition({
    required this.id,
    required this.spotId,
    required this.type,
    required this.value,
    required this.created,
    required this.updated,
  });

  factory SpotVisibilityCondition.fromJson(Map<String, dynamic> json) {
    return SpotVisibilityCondition(
      id: json['id'],
      spotId: json['spot_id'],
      type: VisibilityConditionType.values.firstWhere(
        (t) => t.value == json['condition_type'],
      ),
      value: json['condition_value'] is String 
          ? jsonDecode(json['condition_value'])
          : json['condition_value'],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }
}
```

### 2. FishingSpot模型扩展

```dart
// 在 lib/models/fishing_spot.dart 中添加
class FishingSpot {
  // ... 现有字段 ...
  
  /// 可见性级别
  final SpotVisibility visibility;
  
  /// 可见性条件（JSON格式）
  final Map<String, dynamic>? visibilityConditions;
  
  /// 可见性更新时间
  final DateTime visibilityUpdatedAt;

  FishingSpot({
    // ... 现有参数 ...
    this.visibility = SpotVisibility.public,
    this.visibilityConditions,
    DateTime? visibilityUpdatedAt,
  }) : visibilityUpdatedAt = visibilityUpdatedAt ?? DateTime.now();

  factory FishingSpot.fromJson(Map<String, dynamic> json) {
    return FishingSpot(
      // ... 现有字段映射 ...
      visibility: SpotVisibility.fromString(json['visibility'] ?? 'PUBLIC'),
      visibilityConditions: json['visibility_conditions'] != null
          ? (json['visibility_conditions'] is String
              ? jsonDecode(json['visibility_conditions'])
              : json['visibility_conditions'])
          : null,
      visibilityUpdatedAt: json['visibility_updated_at'] != null
          ? DateTime.parse(json['visibility_updated_at'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // ... 现有字段映射 ...
      'visibility': visibility.value,
      'visibility_conditions': visibilityConditions != null
          ? jsonEncode(visibilityConditions)
          : null,
      'visibility_updated_at': visibilityUpdatedAt.toIso8601String(),
    };
  }
}
```

### 3. 可见性服务

```dart
// lib/services/spot_visibility_service.dart
class SpotVisibilityService {
  static final SpotVisibilityService _instance = SpotVisibilityService._internal();
  factory SpotVisibilityService() => _instance;
  SpotVisibilityService._internal();

  // 缓存用户的访问权限
  final Map<String, Map<String, bool>> _accessCache = {};
  
  /// 检查用户是否可以查看指定钓点
  Future<bool> canUserViewSpot(String userId, FishingSpot spot) async {
    // 缓存键
    final cacheKey = '${userId}_${spot.id}';
    
    // 检查缓存
    if (_accessCache[userId]?.containsKey(spot.id) == true) {
      return _accessCache[userId]![spot.id]!;
    }

    bool hasAccess = false;
    
    switch (spot.visibility) {
      case SpotVisibility.public:
        hasAccess = true;
        break;
        
      case SpotVisibility.private:
        hasAccess = spot.userId == userId;
        break;
        
      case SpotVisibility.friendsOnly:
        hasAccess = await _checkFriendship(userId, spot.userId);
        break;
        
      case SpotVisibility.conditional:
        hasAccess = await _checkConditions(userId, spot);
        break;
    }

    // 缓存结果
    _accessCache[userId] ??= {};
    _accessCache[userId]![spot.id] = hasAccess;
    
    return hasAccess;
  }

  /// 检查用户友谊关系
  Future<bool> _checkFriendship(String userId, String spotOwnerId) async {
    if (userId == spotOwnerId) return true;
    
    try {
      final records = await pb.collection('user_follows').getList(
        filter: 'follower_id = "$userId" && following_id = "$spotOwnerId"',
        perPage: 1,
      );
      return records.items.isNotEmpty;
    } catch (e) {
      debugPrint('检查友谊关系失败: $e');
      return false;
    }
  }

  /// 检查条件可见性
  Future<bool> _checkConditions(String userId, FishingSpot spot) async {
    if (userId == spot.userId) return true;
    
    if (spot.visibilityConditions == null) return false;
    
    final conditions = spot.visibilityConditions!;
    
    // 检查积分赠送条件
    if (conditions['type'] == 'POINTS_DONATED') {
      final requiredPoints = conditions['minPoints'] as int? ?? 0;
      return await _checkPointsDonated(userId, spot.userId, requiredPoints);
    }
    
    // 检查等级要求
    if (conditions['type'] == 'LEVEL_REQUIRED') {
      final requiredLevel = conditions['minLevel'] as int? ?? 0;
      return await _checkUserLevel(userId, requiredLevel);
    }
    
    return false;
  }

  /// 检查用户是否向钓点创建者赠送过足够积分
  Future<bool> _checkPointsDonated(String donorId, String recipientId, int minPoints) async {
    try {
      final records = await pb.collection('user_point_donations').getList(
        filter: 'donor_id = "$donorId" && recipient_id = "$recipientId"',
      );
      
      final totalDonated = records.items.fold<int>(
        0, 
        (sum, record) => sum + (record.data['points'] as int? ?? 0),
      );
      
      return totalDonated >= minPoints;
    } catch (e) {
      debugPrint('检查积分赠送失败: $e');
      return false;
    }
  }

  /// 检查用户等级
  Future<bool> _checkUserLevel(String userId, int requiredLevel) async {
    try {
      final user = await Services.user.getUserById(userId);
      if (user == null) return false;
      
      // 根据积分计算等级（示例逻辑）
      final userLevel = (user.points / 1000).floor() + 1;
      return userLevel >= requiredLevel;
    } catch (e) {
      debugPrint('检查用户等级失败: $e');
      return false;
    }
  }

  /// 清除访问权限缓存
  void clearAccessCache([String? userId]) {
    if (userId != null) {
      _accessCache.remove(userId);
    } else {
      _accessCache.clear();
    }
  }

  /// 设置钓点可见性
  Future<bool> setSpotVisibility(
    String spotId,
    SpotVisibility visibility, {
    Map<String, dynamic>? conditions,
  }) async {
    try {
      final updateData = {
        'visibility': visibility.value,
        'visibility_updated_at': DateTime.now().toIso8601String(),
      };
      
      if (conditions != null) {
        updateData['visibility_conditions'] = jsonEncode(conditions);
      }
      
      await pb.collection('fishing_spots').update(spotId, body: updateData);
      
      // 清除相关缓存
      _accessCache.forEach((userId, spotCache) {
        spotCache.remove(spotId);
      });
      
      return true;
    } catch (e) {
      debugPrint('设置钓点可见性失败: $e');
      return false;
    }
  }
}
```

## 🔧 后端实现（PocketBase Hooks）

### 1. 钓点查询过滤Hook

```javascript
// pb_hooks/fishing_spots_filter.pb.js
routerAdd("GET", "/api/collections/fishing_spots/records", (c) => {
  const userId = c.get("authRecord")?.id;
  if (!userId) {
    return c.json(401, { error: "未授权访问" });
  }

  const page = parseInt(c.queryParam("page") || "1");
  const perPage = parseInt(c.queryParam("perPage") || "30");
  const filter = c.queryParam("filter") || "";

  try {
    // 构建可见性过滤条件
    const visibilityFilter = buildVisibilityFilter(userId);

    // 合并用户自定义过滤条件
    const finalFilter = filter
      ? `(${filter}) && (${visibilityFilter})`
      : visibilityFilter;

    const records = $app.dao.findRecordsByFilter(
      "fishing_spots",
      finalFilter,
      "-created",
      perPage,
      (page - 1) * perPage
    );

    // 进一步过滤条件可见的钓点
    const filteredRecords = [];
    for (const record of records) {
      if (await checkSpotAccess(userId, record)) {
        filteredRecords.push(record);
      }
    }

    return c.json(200, {
      page: page,
      perPage: perPage,
      totalItems: filteredRecords.length,
      totalPages: Math.ceil(filteredRecords.length / perPage),
      items: filteredRecords
    });
  } catch (error) {
    return c.json(500, { error: error.message });
  }
});

function buildVisibilityFilter(userId) {
  return `(
    visibility = 'PUBLIC' ||
    (visibility = 'PRIVATE' && user_id = '${userId}') ||
    (visibility = 'FRIENDS_ONLY' && (
      user_id = '${userId}' ||
      user_id IN (
        SELECT following_id FROM user_follows
        WHERE follower_id = '${userId}'
      )
    )) ||
    visibility = 'CONDITIONAL'
  )`;
}

async function checkSpotAccess(userId, spotRecord) {
  const visibility = spotRecord.get("visibility");
  const spotOwnerId = spotRecord.get("user_id");

  switch (visibility) {
    case 'PUBLIC':
      return true;

    case 'PRIVATE':
      return userId === spotOwnerId;

    case 'FRIENDS_ONLY':
      if (userId === spotOwnerId) return true;
      return checkFriendship(userId, spotOwnerId);

    case 'CONDITIONAL':
      if (userId === spotOwnerId) return true;
      return checkVisibilityConditions(userId, spotRecord);

    default:
      return false;
  }
}
```
